# User Bot Management System

## Overview

This system provides comprehensive management commands for Telegram user bot accounts. You can easily add, verify, remove, backup, and restore user bot accounts through Django management commands.

## Available Commands

### 1. Add New User Bot
```bash
python manage.py add_user_bot <account_name> [options]
```

**Options:**
- `--phone <number>`: Phone number with country code (e.g., +**********)
- `--list-existing`: Show existing accounts before adding
- `--force`: Overwrite existing account session

**Examples:**
```bash
# Add new account with interactive phone input
python manage.py add_user_bot my_new_bot

# Add account with phone number specified
python manage.py add_user_bot account6 --phone +**********

# List existing accounts first, then add new one
python manage.py add_user_bot account7 --list-existing

# Force overwrite existing account
python manage.py add_user_bot existing_account --force
```

### 2. Check Account Status
```bash
python manage.py account_status
```

Shows overview of all discovered accounts with authentication status.

### 3. Manage Existing Accounts
```bash
python manage.py manage_user_bots <action> [options]
```

**Available Actions:**

#### List Accounts
```bash
# Basic list
python manage.py manage_user_bots list

# Detailed list with account info
python manage.py manage_user_bots list --detailed
```

#### Verify Account
```bash
python manage.py manage_user_bots verify <account_name>
```

#### Remove Account
```bash
# Remove account (with confirmation)
python manage.py manage_user_bots remove <account_name>

# Remove with backup
python manage.py manage_user_bots remove <account_name> --backup
```

#### Backup Accounts
```bash
# Backup to default location (./backups)
python manage.py manage_user_bots backup

# Backup to custom location
python manage.py manage_user_bots backup --destination /path/to/backup
```

#### Restore Accounts
```bash
python manage.py manage_user_bots restore /path/to/backup/directory
```

## Authentication Process

When adding a new user bot, the system will:

1. **Connect to Telegram** using your API credentials
2. **Send verification code** to the provided phone number
3. **Prompt for code** that you receive via SMS/Telegram
4. **Handle 2FA** if enabled on the account
5. **Verify authentication** and show account details
6. **Create session file** for future use

### Example Authentication Flow
```
🚀 Starting authentication for account: my_bot
🔗 Connecting to Telegram...
📞 Sending code to +**********...
🔐 Enter the verification code you received: 12345
🔒 Two-factor authentication detected
🔑 Enter your 2FA password: ********
✅ Successfully authenticated account: my_bot
   👤 Name: John Doe
   📱 Phone: +**********
   🆔 ID: *********
```

## Security Considerations

### Session File Security
- **Keep session files private** - they contain authentication tokens
- **Don't share session files** with others
- **Use `.gitignore`** to exclude session files from version control
- **Create regular backups** of important accounts

### Best Practices
1. **Use descriptive account names** (e.g., `main_account`, `backup_bot`)
2. **Verify accounts regularly** to ensure they're still valid
3. **Remove unused accounts** to reduce security exposure
4. **Backup before major changes** to the system

## File Structure

```
project_root/
├── account1.session           # Authenticated account
├── account1.session-journal   # SQLite journal file
├── my_bot.session            # Another authenticated account
├── backups/                  # Backup directory
│   └── userbot_backup_20250722_120000/
│       ├── account1.session
│       └── my_bot.session
└── userbots/
    └── management/
        └── commands/
            ├── add_user_bot.py
            ├── manage_user_bots.py
            ├── account_status.py
            └── run_user_bots.py
```

## Error Handling

### Common Issues and Solutions

#### "Account already exists"
```bash
# Use --force to overwrite
python manage.py add_user_bot existing_account --force
```

#### "Invalid verification code"
- Double-check the code from SMS/Telegram
- Make sure you're entering it quickly (codes expire)
- Try requesting a new code

#### "Session expired or invalid"
```bash
# Re-authenticate the account
python manage.py add_user_bot account_name --force
```

#### "Rate limited by Telegram"
- Wait the specified time before trying again
- Telegram has strict rate limits for authentication

## Integration with Multi-Account System

The user bot management system integrates seamlessly with the multi-account task processing system:

1. **Automatic Discovery**: New accounts are automatically discovered by the main system
2. **Rate Limiting**: Each account respects the 5-minute cooldown and 2-task limit
3. **Load Balancing**: Tasks are distributed across all authenticated accounts
4. **Monitoring**: Account status is tracked in real-time

### Workflow Example
```bash
# 1. Add new accounts
python manage.py add_user_bot worker1 --phone +**********
python manage.py add_user_bot worker2 --phone +**********

# 2. Verify they're working
python manage.py account_status

# 3. Start the multi-account system
python manage.py run_user_bots

# 4. Monitor and manage as needed
python manage.py manage_user_bots list --detailed
```

## Troubleshooting

### Authentication Issues
- Ensure phone number includes country code
- Check that the phone number is registered with Telegram
- Verify API_ID and API_HASH are correct

### Session Issues
- Session files may become corrupted - re-authenticate if needed
- Journal files are automatically managed by SQLite
- Large session files (>50KB) are normal for active accounts

### Permission Issues
- Ensure the script has write permissions in the project directory
- Check that backup directories are writable

## Advanced Usage

### Batch Operations
```bash
# Backup before major changes
python manage.py manage_user_bots backup

# Add multiple accounts (script this)
for i in {1..5}; do
    python manage.py add_user_bot "worker$i"
done

# Verify all accounts
python manage.py manage_user_bots list --detailed
```

### Monitoring Script
```bash
#!/bin/bash
# Check account status and restart if needed
python manage.py account_status
if [ $? -eq 0 ]; then
    echo "All accounts OK"
else
    echo "Issues detected, check logs"
fi
```

## API Reference

### Command Line Arguments

#### add_user_bot
- `account_name` (required): Name for the new account
- `--phone`: Phone number with country code
- `--list-existing`: Show existing accounts first
- `--force`: Overwrite existing session

#### manage_user_bots
- `action` (required): list, verify, remove, backup, restore
- `account_name`: Required for verify/remove actions
- `--detailed`: Show detailed info (list action)
- `--backup`: Create backup before removal
- `--destination`: Custom backup location

### Exit Codes
- `0`: Success
- `1`: General error
- `2`: Authentication failed
- `3`: File operation failed
